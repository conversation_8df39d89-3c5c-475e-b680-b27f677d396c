"use server";

import prisma from "@/lib/prisma";
import { auth } from "@/auth";
import { redirect } from "next/navigation";

// Helper function to check if user is admin
async function checkAdminAccess() {
  const session = await auth();
  
  if (!session?.user) {
    redirect("/login");
  }

  // TODO: Add proper admin role check when you implement role-based access
  // For now, any authenticated user can access admin functions
  // Uncomment this when you have admin roles:
  // if (session.user.role !== 'ADMIN') {
  //   redirect('/dashboard');
  // }

  return session.user;
}

// Get all users with their subscription info
export async function getAllUsers() {
  await checkAdminAccess();

  try {
    const users = await prisma.user.findMany({
      include: {
        subscriptions: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1, // Get latest subscription
        },
        currentPlan: true,
        _count: {
          select: {
            conversations: true,
            subscriptions: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return {
      success: true,
      data: users.map((user) => ({
        id: user.id,
        email: user.email,
        name: user.name,
        username: user.username,
        role: user.role,
        image: user.image,
        subscriptionStatus: user.subscriptionStatus,
        readingsUsed: user.readingsUsed,
        readingsLimit: user.readingsLimit,
        stripeCustomerId: user.stripeCustomerId,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        currentPlan: user.currentPlan,
        latestSubscription: user.subscriptions[0] || null,
        totalConversations: user._count.conversations,
        totalSubscriptions: user._count.subscriptions,
      })),
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    return {
      success: false,
      error: "Failed to fetch users",
    };
  }
}

// Get all subscriptions with user info
export async function getAllSubscriptions() {
  await checkAdminAccess();

  try {
    const subscriptions = await prisma.subscription.findMany({
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            username: true,
            image: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return {
      success: true,
      data: subscriptions,
    };
  } catch (error) {
    console.error("Error fetching subscriptions:", error);
    return {
      success: false,
      error: "Failed to fetch subscriptions",
    };
  }
}

// Get all conversations with user info
export async function getAllConversations() {
  await checkAdminAccess();

  try {
    const conversations = await prisma.conversation.findMany({
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            username: true,
            image: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return {
      success: true,
      data: conversations,
    };
  } catch (error) {
    console.error("Error fetching conversations:", error);
    return {
      success: false,
      error: "Failed to fetch conversations",
    };
  }
}

// Get admin dashboard stats
export async function getAdminStats() {
  await checkAdminAccess();

  try {
    const [
      totalUsers,
      totalSubscriptions,
      totalConversations,
      activeSubscriptions,
      recentUsers,
      recentConversations,
    ] = await Promise.all([
      prisma.user.count(),
      prisma.subscription.count(),
      prisma.conversation.count(),
      prisma.subscription.count({
        where: {
          status: "ACTIVE",
        },
      }),
      prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
          },
        },
      }),
      prisma.conversation.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
          },
        },
      }),
    ]);

    return {
      success: true,
      data: {
        totalUsers,
        totalSubscriptions,
        totalConversations,
        activeSubscriptions,
        recentUsers,
        recentConversations,
      },
    };
  } catch (error) {
    console.error("Error fetching admin stats:", error);
    return {
      success: false,
      error: "Failed to fetch admin statistics",
    };
  }
}

// Get user details by ID
export async function getUserById(userId: string) {
  await checkAdminAccess();

  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          orderBy: {
            createdAt: "desc",
          },
        },
        conversations: {
          orderBy: {
            createdAt: "desc",
          },
          take: 10, // Latest 10 conversations
        },
        currentPlan: true,
        accounts: true,
      },
    });

    if (!user) {
      return {
        success: false,
        error: "User not found",
      };
    }

    return {
      success: true,
      data: user,
    };
  } catch (error) {
    console.error("Error fetching user details:", error);
    return {
      success: false,
      error: "Failed to fetch user details",
    };
  }
}
